"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
	Toolt<PERSON>,
	TooltipContent,
	TooltipTrigger,
	TooltipProvider,
} from "@/components/ui/tooltip";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuGroup,
	DropdownMenuItem,
	DropdownMenuLabel,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Link, useNavigate } from "@tanstack/react-router";
import {
	Bell,
	Globe,
	HelpCircle,
	ChevronRight,
	LogOut,
	User,
	Settings,
} from "react-feather";
import { useAuthStore } from "@/features/auth/store";
import { useShallow } from "zustand/react/shallow";
import { useGetUser, useGetProfilePictureUrl } from "@/lib/queries/user.query";

// Dynamic menu items
const userNavMenus = [
	{ href: "/account", label: "Account", icon: User },
	{
		href: "",
		label: "Notifications",
		icon: Bell,
		mobileOnly: true,
		disabled: true,
	},
	{ href: "", label: "Language", icon: Globe, disabled: true },
	{ href: "", label: "Settings", icon: Settings, disabled: true },
	{ href: "/help", label: "Help", icon: HelpCircle },
];

export function UserNav() {
	const navigate = useNavigate();
	const { user, logout } = useAuthStore(
		useShallow((state) => ({
			user: state.user,
			logout: state.logout,
		}))
	);

	// Fetch user details from API using firebase ID
	const { data: userData } = useGetUser(user?.uid || "");
	const apiUser = userData?.data?.data;

	// Get profile picture URL
	const { data: profilePictureData } = useGetProfilePictureUrl(user?.uid || "");
	const profilePictureUrl = profilePictureData?.data?.data?.url;

	// Extract user details - prioritize database name if it exists and is not empty
	const displayName =
		apiUser?.name && apiUser.name.trim() !== ""
			? apiUser.name
			: user?.displayName || user?.email?.split("@")[0] || "User";

	const handleLogout = async () => {
		await logout();
		// Navigate to login page after logout
		navigate({ to: "/account/login" });
	};

	const handleMenuClick = (label: string) => {
		switch (label) {
			case "Logout":
				handleLogout();
				break;
			default:
				break;
		}
	};

	// Get user initials for avatar fallback
	const getInitials = (name: string) => {
		if (!name) return "??";
		return name
			.split(" ")
			.map((part) => part.charAt(0).toUpperCase())
			.join("")
			.substring(0, 2);
	};

	return (
		<DropdownMenu>
			<TooltipProvider disableHoverableContent>
				<Tooltip delayDuration={100}>
					<TooltipTrigger asChild>
						<DropdownMenuTrigger asChild>
							<Button variant="ghost" className="px-0 hover:bg-transparent">
								<div className="flex items-center gap-x-3 w-full">
									<Avatar className="size-[42px]">
										<AvatarImage src={profilePictureUrl || ""} alt="Avatar" />
										<AvatarFallback>{getInitials(displayName)}</AvatarFallback>
									</Avatar>
									<div className="flex flex-col items-start justify-between">
										<p className="text-base font-medium text-white lg:text-foreground">
											{displayName}
										</p>
										<p className="text-sm font-normal lg:hidden text-white truncate">
											{/* {user?.email} */}
											<EMAIL>
										</p>
									</div>
									<ChevronRight
										size={20}
										className="text-white lg:text-foreground"
									/>
								</div>
							</Button>
						</DropdownMenuTrigger>
					</TooltipTrigger>
					<TooltipContent className="hidden lg:block" side="bottom">
						Profile
					</TooltipContent>
				</Tooltip>
			</TooltipProvider>
			<DropdownMenuContent className="w-56" align="end" forceMount>
				<DropdownMenuLabel className="font-normal">
					<div className="flex flex-col space-y-1">
						<p className="text-sm font-medium leading-none">{displayName}</p>
						<p className="text-xs leading-none text-slate-500 dark:text-slate-400">
							{user?.email}
						</p>
					</div>
				</DropdownMenuLabel>
				<DropdownMenuSeparator />
				<DropdownMenuGroup>
					{userNavMenus.map(
						({ href, label, icon: Icon, mobileOnly, disabled }, index) => (
							<DropdownMenuItem
								key={index}
								className={`${mobileOnly ? "lg:hidden" : ""} ${!disabled ? "cursor-pointer focus:bg-gray-200" : "opacity-50 cursor-not-allowed focus:bg-transparent"}`}
								asChild={href.length > 0}
								onClick={
									!disabled && href.length === 0
										? () => handleMenuClick(label)
										: undefined
								}
							>
								{href.length > 0 ? (
									<Link to={href}>
										<Icon className="w-4 h-4 mr-3 text-slate-500 dark:text-slate-400" />
										{label}
									</Link>
								) : (
									<>
										<Icon className="w-4 h-4 mr-3 text-slate-500 dark:text-slate-400" />
										{label}
									</>
								)}
							</DropdownMenuItem>
						)
					)}
				</DropdownMenuGroup>
				<DropdownMenuSeparator />
				<DropdownMenuItem
					className="hover:cursor-pointer text-red-500 focus:text-red-500 focus:bg-gray-200"
					onClick={handleLogout}
				>
					<LogOut className="w-4 h-4 mr-3 text-red-500" />
					Logout
				</DropdownMenuItem>
			</DropdownMenuContent>
		</DropdownMenu>
	);
}
